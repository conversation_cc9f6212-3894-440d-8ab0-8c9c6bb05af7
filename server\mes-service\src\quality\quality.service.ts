import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { QualityInspection, QualityControlPlan, InspectionStatus, InspectionResult } from './entities/quality-inspection.entity';

/**
 * 质量管理服务
 */
@Injectable()
export class QualityService {
  private readonly logger = new Logger(QualityService.name);

  constructor(
    @InjectRepository(QualityInspection)
    private readonly qualityInspectionRepository: Repository<QualityInspection>,
    @InjectRepository(QualityControlPlan)
    private readonly qualityControlPlanRepository: Repository<QualityControlPlan>,
  ) {}

  /**
   * 创建质量检验
   */
  async createInspection(createInspectionDto: any): Promise<QualityInspection> {
    try {
      const inspection = this.qualityInspectionRepository.create(createInspectionDto);
      const savedInspection = await this.qualityInspectionRepository.save(inspection) as unknown as QualityInspection;
      this.logger.log(`质量检验创建成功: ${savedInspection.inspectionNumber}`);
      return savedInspection;
    } catch (error) {
      this.logger.error(`创建质量检验失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 获取检验列表
   */
  async getInspections(query: any) {
    try {
      const { page = 1, limit = 10 } = query;
      const skip = (page - 1) * limit;

      const [data, total] = await this.qualityInspectionRepository.findAndCount({
        skip,
        take: limit,
        order: { createdAt: 'DESC' },
      });

      return {
        data,
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      };
    } catch (error) {
      this.logger.error(`获取检验列表失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 根据ID获取检验详情
   */
  async getInspectionById(id: string): Promise<QualityInspection> {
    try {
      const inspection = await this.qualityInspectionRepository.findOne({
        where: { id },
      });

      if (!inspection) {
        throw new NotFoundException(`质量检验不存在: ${id}`);
      }

      return inspection;
    } catch (error) {
      this.logger.error(`获取检验详情失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 更新检验
   */
  async updateInspection(id: string, updateInspectionDto: any): Promise<QualityInspection> {
    try {
      const inspection = await this.getInspectionById(id);
      Object.assign(inspection, updateInspectionDto);
      const updatedInspection = await this.qualityInspectionRepository.save(inspection);
      this.logger.log(`质量检验更新成功: ${updatedInspection.inspectionNumber}`);
      return updatedInspection;
    } catch (error) {
      this.logger.error(`更新质量检验失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 删除检验
   */
  async deleteInspection(id: string): Promise<void> {
    try {
      const inspection = await this.getInspectionById(id);
      await this.qualityInspectionRepository.remove(inspection);
      this.logger.log(`质量检验删除成功: ${inspection.inspectionNumber}`);
    } catch (error) {
      this.logger.error(`删除质量检验失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 开始检验
   */
  async startInspection(id: string): Promise<QualityInspection> {
    try {
      const inspection = await this.getInspectionById(id);
      inspection.status = InspectionStatus.IN_PROGRESS;
      inspection.actualStartTime = new Date();
      const updatedInspection = await this.qualityInspectionRepository.save(inspection);
      this.logger.log(`质量检验开始: ${updatedInspection.inspectionNumber}`);
      return updatedInspection;
    } catch (error) {
      this.logger.error(`开始质量检验失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 完成检验
   */
  async completeInspection(id: string, result: InspectionResult): Promise<QualityInspection> {
    try {
      const inspection = await this.getInspectionById(id);
      inspection.status = InspectionStatus.COMPLETED;
      inspection.inspectionResult = result;
      inspection.actualEndTime = new Date();
      const updatedInspection = await this.qualityInspectionRepository.save(inspection);
      this.logger.log(`质量检验完成: ${updatedInspection.inspectionNumber}, 结果: ${result}`);
      return updatedInspection;
    } catch (error) {
      this.logger.error(`完成质量检验失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 获取质量统计
   */
  async getQualityStatistics() {
    try {
      const totalInspections = await this.qualityInspectionRepository.count();
      
      const inspectionsByStatus = await this.qualityInspectionRepository
        .createQueryBuilder('inspection')
        .select('inspection.status', 'status')
        .addSelect('COUNT(*)', 'count')
        .groupBy('inspection.status')
        .getRawMany();

      const inspectionsByResult = await this.qualityInspectionRepository
        .createQueryBuilder('inspection')
        .select('inspection.inspectionResult', 'result')
        .addSelect('COUNT(*)', 'count')
        .where('inspection.inspectionResult IS NOT NULL')
        .groupBy('inspection.inspectionResult')
        .getRawMany();

      const qualityMetrics = await this.qualityInspectionRepository
        .createQueryBuilder('inspection')
        .select('AVG(inspection.qualifiedQuantity / inspection.inspectionQuantity * 100)', 'avgQualityRate')
        .addSelect('AVG(inspection.defectiveQuantity / inspection.inspectionQuantity * 100)', 'avgDefectRate')
        .where('inspection.inspectionQuantity > 0')
        .getRawOne();

      return {
        totalInspections,
        inspectionsByStatus: inspectionsByStatus.reduce((acc, item) => {
          acc[item.status] = parseInt(item.count);
          return acc;
        }, {}),
        inspectionsByResult: inspectionsByResult.reduce((acc, item) => {
          acc[item.result] = parseInt(item.count);
          return acc;
        }, {}),
        avgQualityRate: parseFloat(qualityMetrics.avgQualityRate) || 0,
        avgDefectRate: parseFloat(qualityMetrics.avgDefectRate) || 0,
      };
    } catch (error) {
      this.logger.error(`获取质量统计失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 创建质控计划
   */
  async createControlPlan(createPlanDto: any): Promise<QualityControlPlan> {
    try {
      const plan = this.qualityControlPlanRepository.create(createPlanDto);
      const savedPlan = await this.qualityControlPlanRepository.save(plan) as QualityControlPlan;
      this.logger.log(`质控计划创建成功: ${savedPlan.planNumber}`);
      return savedPlan;
    } catch (error) {
      this.logger.error(`创建质控计划失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 获取质控计划列表
   */
  async getControlPlans(query: any) {
    try {
      const { page = 1, limit = 10 } = query;
      const skip = (page - 1) * limit;

      const [data, total] = await this.qualityControlPlanRepository.findAndCount({
        skip,
        take: limit,
        order: { createdAt: 'DESC' },
      });

      return {
        data,
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      };
    } catch (error) {
      this.logger.error(`获取质控计划列表失败: ${error.message}`, error.stack);
      throw error;
    }
  }
}
